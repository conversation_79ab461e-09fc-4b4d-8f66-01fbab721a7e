#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单臂路由配置部署脚本
用于在eNSP环境中快速部署生成的配置
"""

import os
import json
import time
from datetime import datetime

class ConfigDeployer:
    """配置部署器"""
    
    def __init__(self):
        self.config_dir = "single_arm_routing_configs"
        self.deployment_log = []
        
    def load_config_data(self):
        """加载配置数据"""
        try:
            config_file = os.path.join(self.config_dir, "complete_config.json")
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置数据失败: {e}")
            return None
    
    def print_deployment_guide(self):
        """打印部署指南"""
        print("="*70)
        print("单臂路由配置部署指南".center(70))
        print("="*70)
        
        print(f"""
📋 部署步骤概览:

1️⃣  启动eNSP并打开拓扑文件 (1.topo)
2️⃣  启动所有设备 (AR1, LSW1, LSW2)
3️⃣  按顺序配置设备:
    - AR1 路由器 (单臂路由配置)
    - LSW1 交换机 (VLAN中继)
    - LSW2 交换机 (VLAN接入)
4️⃣  配置PC网络设置
5️⃣  验证网络连通性

⚠️  注意事项:
- 确保所有设备已正常启动
- 按照指定顺序进行配置
- 每个设备配置完成后保存配置
- 配置过程中注意接口编号对应关系
        """)
    
    def show_device_config(self, device_name):
        """显示设备配置"""
        config_file = os.path.join(self.config_dir, f"{device_name.lower()}_config.txt")
        
        if not os.path.exists(config_file):
            print(f"❌ 配置文件 {config_file} 不存在")
            return False
        
        print(f"\n{'='*50}")
        print(f"{device_name} 配置内容".center(50))
        print(f"{'='*50}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_content = f.read()
            
            print(config_content)
            
            print(f"\n{'='*50}")
            print(f"📋 配置说明:")
            
            if device_name == "AR1":
                print("""
🔀 AR1路由器配置要点:
- 配置子接口实现单臂路由
- Ethernet0/0/0.10 作为VLAN10网关 (************)
- Ethernet0/0/0.20 作为VLAN20网关 (************)
- 启用ARP广播确保正常通信

⚙️ 配置步骤:
1. 进入系统视图: system-view
2. 逐行输入配置命令
3. 保存配置: save
                """)
            
            elif device_name == "LSW1":
                print("""
🔄 LSW1交换机配置要点:
- 创建VLAN 10和VLAN 20
- GE0/0/1配置为Trunk连接路由器
- GE0/0/2配置为Trunk连接LSW2
- 启用STP防止环路

⚙️ 配置步骤:
1. 进入系统视图: system-view
2. 创建VLAN并配置接口
3. 保存配置: save
                """)
            
            elif device_name == "LSW2":
                print("""
🔄 LSW2交换机配置要点:
- 创建VLAN 10和VLAN 20
- GE0/0/1配置为Trunk连接LSW1
- Eth0/0/1配置为Access VLAN10 (连接PC1)
- Eth0/0/2配置为Access VLAN20 (连接PC2)

⚙️ 配置步骤:
1. 进入系统视图: system-view
2. 创建VLAN并配置接口
3. 保存配置: save
                """)
            
            return True
            
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            return False
    
    def show_pc_config_guide(self):
        """显示PC配置指南"""
        print(f"\n{'='*50}")
        print("PC网络配置指南".center(50))
        print(f"{'='*50}")
        
        print("""
💻 PC1 网络配置:
IP地址: *************
子网掩码: *************
默认网关: ************
DNS服务器: *******

Windows配置命令:
netsh interface ip set address "本地连接" static ************* ************* ************
netsh interface ip set dns "本地连接" static *******

💻 PC2 网络配置:
IP地址: *************
子网掩码: *************
默认网关: ************
DNS服务器: *******

Windows配置命令:
netsh interface ip set address "本地连接" static ************* ************* ************
netsh interface ip set dns "本地连接" static *******

📝 eNSP中PC配置方法:
1. 双击PC设备打开配置界面
2. 在IP Configuration标签页中设置IP地址信息
3. 或者在Command Line中执行上述命令
        """)
    
    def show_verification_guide(self):
        """显示验证指南"""
        print(f"\n{'='*50}")
        print("网络连通性验证指南".center(50))
        print(f"{'='*50}")
        
        print("""
🧪 验证步骤:

1️⃣  基本连通性测试:
   PC1 ping 网关: ping ************
   PC2 ping 网关: ping ************

2️⃣  跨VLAN通信测试:
   PC1 ping PC2: ping *************
   PC2 ping PC1: ping *************

3️⃣  设备状态检查:
   AR1路由器:
   - display ip routing-table
   - display interface brief
   - display interface Ethernet0/0/0.10
   - display interface Ethernet0/0/0.20
   
   交换机 (LSW1/LSW2):
   - display vlan
   - display port vlan
   - display stp brief
   - display interface brief

✅ 预期结果:
- 所有ping测试成功
- 路由表显示直连路由
- VLAN配置正确
- STP状态正常

❌ 故障排除:
- 检查接口状态 (up/down)
- 检查VLAN配置
- 检查IP地址配置
- 检查物理连接
        """)
    
    def interactive_deployment(self):
        """交互式部署"""
        print("🚀 开始交互式配置部署...")
        
        devices = ["AR1", "LSW1", "LSW2"]
        
        for device in devices:
            print(f"\n{'🔧 ' + device + ' 配置部署'}")
            print("-" * 30)
            
            while True:
                choice = input(f"是否显示 {device} 配置? (y/n/q退出): ").lower().strip()
                
                if choice == 'q':
                    print("退出部署...")
                    return
                elif choice == 'y':
                    self.show_device_config(device)
                    input(f"\n按回车键继续配置下一个设备...")
                    break
                elif choice == 'n':
                    print(f"跳过 {device} 配置显示")
                    break
                else:
                    print("请输入 y/n/q")
        
        # PC配置指南
        choice = input(f"\n是否显示PC配置指南? (y/n): ").lower().strip()
        if choice == 'y':
            self.show_pc_config_guide()
        
        # 验证指南
        choice = input(f"\n是否显示验证指南? (y/n): ").lower().strip()
        if choice == 'y':
            self.show_verification_guide()
        
        print(f"\n🎉 配置部署指南完成!")
    
    def generate_deployment_checklist(self):
        """生成部署检查清单"""
        checklist = [
            "# 单臂路由配置部署检查清单",
            f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 准备工作",
            "[ ] 启动eNSP软件",
            "[ ] 打开拓扑文件 (1.topo)",
            "[ ] 启动所有设备 (AR1, LSW1, LSW2)",
            "[ ] 确认设备启动完成",
            "",
            "## AR1路由器配置",
            "[ ] 进入系统视图 (system-view)",
            "[ ] 配置系统名称 (sysname AR1)",
            "[ ] 配置主接口 Ethernet0/0/0",
            "[ ] 配置子接口 Ethernet0/0/0.10 (VLAN10)",
            "[ ] 配置子接口 Ethernet0/0/0.20 (VLAN20)",
            "[ ] 保存配置 (save)",
            "",
            "## LSW1交换机配置",
            "[ ] 进入系统视图 (system-view)",
            "[ ] 配置系统名称 (sysname LSW1)",
            "[ ] 创建VLAN (vlan batch 10 20)",
            "[ ] 配置Trunk接口 GE0/0/1 (连接AR1)",
            "[ ] 配置Trunk接口 GE0/0/2 (连接LSW2)",
            "[ ] 启用STP (stp enable)",
            "[ ] 保存配置 (save)",
            "",
            "## LSW2交换机配置",
            "[ ] 进入系统视图 (system-view)",
            "[ ] 配置系统名称 (sysname LSW2)",
            "[ ] 创建VLAN (vlan batch 10 20)",
            "[ ] 配置Trunk接口 GE0/0/1 (连接LSW1)",
            "[ ] 配置Access接口 Eth0/0/1 VLAN10 (连接PC1)",
            "[ ] 配置Access接口 Eth0/0/2 VLAN20 (连接PC2)",
            "[ ] 启用STP (stp enable)",
            "[ ] 保存配置 (save)",
            "",
            "## PC配置",
            "[ ] PC1: IP *************, 网关 ************",
            "[ ] PC2: IP *************, 网关 ************",
            "",
            "## 验证测试",
            "[ ] PC1 ping 网关 (************)",
            "[ ] PC2 ping 网关 (************)",
            "[ ] PC1 ping PC2 (*************)",
            "[ ] PC2 ping PC1 (*************)",
            "[ ] 检查路由表 (display ip routing-table)",
            "[ ] 检查VLAN状态 (display vlan)",
            "[ ] 检查STP状态 (display stp brief)",
            "",
            "## 完成确认",
            "[ ] 所有ping测试成功",
            "[ ] 网络配置正确",
            "[ ] 保存所有设备配置",
            "[ ] 记录配置完成时间",
            "",
            "---",
            "配置完成签名: ________________  日期: ________________"
        ]
        
        try:
            with open("deployment_checklist.md", 'w', encoding='utf-8') as f:
                f.write('\n'.join(checklist))
            print("✅ 部署检查清单已保存到: deployment_checklist.md")
        except Exception as e:
            print(f"❌ 保存检查清单失败: {e}")

def main():
    """主函数"""
    deployer = ConfigDeployer()
    
    # 检查配置文件是否存在
    if not os.path.exists(deployer.config_dir):
        print("❌ 配置文件目录不存在，请先运行 single_arm_routing_config.py 生成配置")
        return
    
    # 显示部署指南
    deployer.print_deployment_guide()
    
    while True:
        print(f"\n{'='*50}")
        print("配置部署选项".center(50))
        print(f"{'='*50}")
        print("1. 交互式配置部署")
        print("2. 显示AR1配置")
        print("3. 显示LSW1配置")
        print("4. 显示LSW2配置")
        print("5. 显示PC配置指南")
        print("6. 显示验证指南")
        print("7. 生成部署检查清单")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-7): ").strip()
        
        if choice == '0':
            print("退出配置部署工具")
            break
        elif choice == '1':
            deployer.interactive_deployment()
        elif choice == '2':
            deployer.show_device_config("AR1")
        elif choice == '3':
            deployer.show_device_config("LSW1")
        elif choice == '4':
            deployer.show_device_config("LSW2")
        elif choice == '5':
            deployer.show_pc_config_guide()
        elif choice == '6':
            deployer.show_verification_guide()
        elif choice == '7':
            deployer.generate_deployment_checklist()
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
