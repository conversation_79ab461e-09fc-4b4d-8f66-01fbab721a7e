#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示eNSP解析结果的总结脚本
"""

import json
import os
from datetime import datetime

def print_separator(title: str, char: str = "=", width: int = 60):
    """打印分隔线"""
    print(f"\n{char * width}")
    print(f"{title:^{width}}")
    print(f"{char * width}")

def show_topology_summary():
    """显示拓扑摘要"""
    try:
        with open("ensp_parse_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print_separator("eNSP拓扑图文件与EFZ文档解析结果")
        
        # 基本信息
        parse_time = results.get('parse_time', 'N/A')
        print(f"📅 解析时间: {parse_time}")
        print(f"📁 工作目录: {results.get('workspace', 'N/A')}")
        
        # 拓扑信息
        topology = results.get('topology', {})
        print(f"\n🌐 拓扑版本: {topology.get('version', 'N/A')}")
        print(f"🖥️  设备总数: {topology.get('device_count', 0)}")
        print(f"🔗 连接总数: {topology.get('connection_count', 0)}")
        
        # 设备详情
        print_separator("设备详细信息", "-")
        devices = topology.get('devices', {})
        
        for i, (device_id, device) in enumerate(devices.items(), 1):
            name = device['name']
            model = device['model']
            mac = device['system_mac']
            port = device['com_port']
            position = device.get('position', {})
            
            # 计算接口总数
            total_interfaces = 0
            for slot in device.get('interfaces', []):
                for interface in slot.get('interfaces', []):
                    total_interfaces += interface.get('count', 0)
            
            print(f"\n{i}. 📱 {name} ({model})")
            print(f"   MAC地址: {mac}")
            print(f"   通信端口: {port}")
            print(f"   接口数量: {total_interfaces}")
            print(f"   位置坐标: ({position.get('x', 0):.0f}, {position.get('y', 0):.0f})")
            
            # PC配置信息
            if 'pc_config' in device:
                pc_config = device['pc_config']
                print(f"   💻 PC配置:")
                print(f"      IP地址: {pc_config.get('simpc_ip', 'N/A')}")
                print(f"      子网掩码: {pc_config.get('simpc_mask', 'N/A')}")
                print(f"      网关: {pc_config.get('simpc_gateway', 'N/A')}")
                print(f"      DHCP状态: {'启用' if pc_config.get('simpc_dhcp_state') == '1' else '禁用'}")
        
        # 连接信息
        print_separator("网络连接信息", "-")
        connections = topology.get('connections', [])
        
        for i, connection in enumerate(connections, 1):
            src_id = connection['src_device_id']
            dest_id = connection['dest_device_id']
            
            src_name = devices.get(src_id, {}).get('name', 'Unknown')
            dest_name = devices.get(dest_id, {}).get('name', 'Unknown')
            
            print(f"\n{i}. 🔗 {src_name} ↔ {dest_name}")
            
            for pair in connection.get('interface_pairs', []):
                line_type = pair.get('line_name', 'Unknown')
                src_index = pair.get('src_index', 0)
                dest_index = pair.get('dest_index', 0)
                print(f"   连接类型: {line_type}")
                print(f"   接口对: {src_name}[{src_index}] ↔ {dest_name}[{dest_index}]")
        
        # EFZ配置文件信息
        print_separator("EFZ配置文件分析", "-")
        devices_config = results.get('devices_config', {})
        
        if devices_config:
            for device_id, config in devices_config.items():
                device_name = devices.get(device_id, {}).get('name', device_id[:8])
                file_path = config.get('file_path', 'N/A')
                file_size = config.get('file_size', 0)
                is_valid = config.get('is_valid_efz', False)
                config_files = config.get('config_files', [])
                
                print(f"\n📄 {device_name} 配置文件:")
                print(f"   文件路径: {file_path}")
                print(f"   文件大小: {file_size:,} 字节")
                print(f"   格式有效: {'✅' if is_valid else '❌'}")
                
                if config_files:
                    print(f"   包含配置: {', '.join(config_files)}")
                
                # 显示文件头信息
                header_info = config.get('header_info', {})
                if header_info:
                    signature = header_info.get('signature', 'N/A')
                    print(f"   文件签名: {signature}")
        else:
            print("❌ 未找到EFZ配置文件")
        
        # 统计摘要
        print_separator("统计摘要", "-")
        summary = results.get('summary', {})
        
        print("📊 设备类型分布:")
        for device_type, count in summary.get('device_types', {}).items():
            print(f"   {device_type}: {count} 台")
        
        print("\n🔗 连接类型分布:")
        for conn_type, count in summary.get('connection_types', {}).items():
            print(f"   {conn_type}: {count} 条")
        
        print(f"\n📁 EFZ文件数量: {summary.get('efz_files_parsed', 0)}")
        
        # 文件列表
        print_separator("生成的文件", "-")
        generated_files = [
            ("ensp_parse_results.json", "完整的JSON格式解析结果"),
            ("topology_report.md", "Markdown格式的拓扑报告"),
            ("topology_diagram.mmd", "Mermaid格式的拓扑图"),
            ("ensp_parser.py", "完整的解析器脚本"),
            ("test_parser.py", "简化版解析器"),
            ("generate_topology_diagram.py", "拓扑图生成脚本")
        ]
        
        for filename, description in generated_files:
            if os.path.exists(filename):
                file_size = os.path.getsize(filename)
                print(f"✅ {filename} ({file_size:,} 字节) - {description}")
            else:
                print(f"❌ {filename} - {description}")
        
        print_separator("解析完成", "=")
        print("🎉 eNSP拓扑图文件与EFZ文档解析成功完成!")
        print("📋 详细信息请查看生成的JSON和Markdown报告文件")
        print("🖼️  网络拓扑图已生成，可在支持Mermaid的编辑器中查看")
        
    except FileNotFoundError:
        print("❌ 未找到解析结果文件 ensp_parse_results.json")
        print("请先运行 ensp_parser.py 进行解析")
    except Exception as e:
        print(f"❌ 显示结果时出错: {e}")

def show_network_topology_ascii():
    """显示ASCII格式的网络拓扑"""
    try:
        with open("ensp_parse_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print_separator("ASCII网络拓扑图", "-")
        
        devices = results.get('topology', {}).get('devices', {})
        connections = results.get('topology', {}).get('connections', [])
        
        # 简单的ASCII拓扑图
        print("""
        网络拓扑结构:
        
        🔀 AR1 (AR201)
        │
        │ Copper(0↔0)
        │
        🔄 LSW1 (S5700)
        │
        │ Copper(1↔0)
        │
        🔄 LSW2 (S3700)
        ├─── Copper(1↔0) ──── 💻 PC1
        │
        └─── Copper(2↔0) ──── 💻 PC2
        """)
        
    except Exception as e:
        print(f"❌ 生成ASCII拓扑图时出错: {e}")

if __name__ == "__main__":
    show_topology_summary()
    show_network_topology_ascii()
