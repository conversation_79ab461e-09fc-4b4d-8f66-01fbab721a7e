#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单臂路由配置生成器
基于当前网络拓扑生成单臂路由配置文件
"""

import json
import os
from datetime import datetime

class SingleArmRoutingConfig:
    """单臂路由配置生成器"""
    
    def __init__(self):
        self.config = {
            'generated_time': datetime.now().isoformat(),
            'topology_info': {},
            'vlan_config': {},
            'device_configs': {},
            'ip_plan': {}
        }
        
        # 定义VLAN和IP地址规划
        self.vlan_plan = {
            'vlan10': {
                'name': 'VLAN_PC1',
                'network': '************/24',
                'gateway': '************',
                'description': 'PC1网段'
            },
            'vlan20': {
                'name': 'VLAN_PC2', 
                'network': '************/24',
                'gateway': '************',
                'description': 'PC2网段'
            }
        }
    
    def load_topology_data(self):
        """加载拓扑数据"""
        try:
            with open('ensp_parse_results.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.config['topology_info'] = data.get('topology', {})
            return True
        except Exception as e:
            print(f"加载拓扑数据失败: {e}")
            return False
    
    def generate_router_config(self):
        """生成路由器(AR1)配置"""
        router_config = [
            "# AR1 (AR201) 单臂路由配置",
            "# 配置生成时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "",
            "# 系统配置",
            "sysname AR1",
            "",
            "# 配置子接口实现单臂路由",
            "interface Ethernet0/0/0",
            " description 连接到LSW1-GE0/0/0",
            " undo shutdown",
            "",
            "# VLAN 10 子接口配置 (PC1网段)",
            "interface Ethernet0/0/0.10",
            " description VLAN10-PC1网段",
            " dot1q termination vid 10",
            " ip address ************ *************",
            " arp broadcast enable",
            "",
            "# VLAN 20 子接口配置 (PC2网段)", 
            "interface Ethernet0/0/0.20",
            " description VLAN20-PC2网段",
            " dot1q termination vid 20",
            " ip address ************ *************",
            " arp broadcast enable",
            "",
            "# 配置默认路由(如果需要外网访问)",
            "# ip route-static 0.0.0.0 0.0.0.0 <外网网关IP>",
            "",
            "# 保存配置",
            "save",
            "quit"
        ]
        
        self.config['device_configs']['AR1'] = {
            'device_type': 'router',
            'model': 'AR201',
            'config_lines': router_config,
            'interfaces': {
                'Ethernet0/0/0': {
                    'description': '连接到LSW1',
                    'type': 'trunk',
                    'vlans': [10, 20]
                },
                'Ethernet0/0/0.10': {
                    'description': 'VLAN10子接口',
                    'ip': '************/24',
                    'vlan': 10
                },
                'Ethernet0/0/0.20': {
                    'description': 'VLAN20子接口', 
                    'ip': '************/24',
                    'vlan': 20
                }
            }
        }
        
        return router_config
    
    def generate_switch_lsw1_config(self):
        """生成LSW1交换机配置"""
        switch_config = [
            "# LSW1 (S5700) 配置",
            "# 配置生成时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "",
            "# 系统配置",
            "sysname LSW1",
            "",
            "# 创建VLAN",
            "vlan batch 10 20",
            "",
            "vlan 10",
            " description PC1网段",
            " quit",
            "",
            "vlan 20", 
            " description PC2网段",
            " quit",
            "",
            "# 配置连接路由器的接口为Trunk",
            "interface GigabitEthernet0/0/1",
            " description 连接到AR1-Ethernet0/0/0",
            " port link-type trunk",
            " port trunk allow-pass vlan 10 20",
            " undo shutdown",
            "",
            "# 配置连接LSW2的接口为Trunk",
            "interface GigabitEthernet0/0/2",
            " description 连接到LSW2-GE0/0/1",
            " port link-type trunk", 
            " port trunk allow-pass vlan 10 20",
            " undo shutdown",
            "",
            "# 配置STP防止环路",
            "stp enable",
            "stp mode rstp",
            "",
            "# 保存配置",
            "save",
            "quit"
        ]
        
        self.config['device_configs']['LSW1'] = {
            'device_type': 'switch',
            'model': 'S5700',
            'config_lines': switch_config,
            'vlans': [10, 20],
            'interfaces': {
                'GigabitEthernet0/0/1': {
                    'description': '连接到AR1',
                    'type': 'trunk',
                    'vlans': [10, 20]
                },
                'GigabitEthernet0/0/2': {
                    'description': '连接到LSW2',
                    'type': 'trunk',
                    'vlans': [10, 20]
                }
            }
        }
        
        return switch_config
    
    def generate_switch_lsw2_config(self):
        """生成LSW2交换机配置"""
        switch_config = [
            "# LSW2 (S3700) 配置",
            "# 配置生成时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "",
            "# 系统配置",
            "sysname LSW2",
            "",
            "# 创建VLAN",
            "vlan batch 10 20",
            "",
            "vlan 10",
            " description PC1网段",
            " quit",
            "",
            "vlan 20",
            " description PC2网段", 
            " quit",
            "",
            "# 配置连接LSW1的接口为Trunk",
            "interface GigabitEthernet0/0/1",
            " description 连接到LSW1-GE0/0/2",
            " port link-type trunk",
            " port trunk allow-pass vlan 10 20",
            " undo shutdown",
            "",
            "# 配置连接PC1的接口为Access VLAN 10",
            "interface Ethernet0/0/1",
            " description 连接到PC1",
            " port link-type access",
            " port default vlan 10",
            " undo shutdown",
            "",
            "# 配置连接PC2的接口为Access VLAN 20",
            "interface Ethernet0/0/2",
            " description 连接到PC2",
            " port link-type access", 
            " port default vlan 20",
            " undo shutdown",
            "",
            "# 配置STP防止环路",
            "stp enable",
            "stp mode rstp",
            "",
            "# 保存配置",
            "save",
            "quit"
        ]
        
        self.config['device_configs']['LSW2'] = {
            'device_type': 'switch',
            'model': 'S3700',
            'config_lines': switch_config,
            'vlans': [10, 20],
            'interfaces': {
                'GigabitEthernet0/0/1': {
                    'description': '连接到LSW1',
                    'type': 'trunk',
                    'vlans': [10, 20]
                },
                'Ethernet0/0/1': {
                    'description': '连接到PC1',
                    'type': 'access',
                    'vlan': 10
                },
                'Ethernet0/0/2': {
                    'description': '连接到PC2',
                    'type': 'access',
                    'vlan': 20
                }
            }
        }
        
        return switch_config
    
    def generate_pc_config(self):
        """生成PC配置"""
        pc_configs = {
            'PC1': {
                'ip': '************0',
                'netmask': '*************',
                'gateway': '************',
                'dns': '*******',
                'vlan': 10,
                'config_commands': [
                    "# PC1 网络配置",
                    "# IP地址: ************0",
                    "# 子网掩码: *************", 
                    "# 默认网关: ************",
                    "# DNS服务器: *******",
                    "",
                    "# Windows命令行配置:",
                    "netsh interface ip set address \"本地连接\" static ************0 ************* ************",
                    "netsh interface ip set dns \"本地连接\" static *******",
                    "",
                    "# Linux命令行配置:",
                    "# ifconfig eth0 ************0 netmask *************",
                    "# route add default gw ************",
                    "# echo 'nameserver *******' > /etc/resolv.conf"
                ]
            },
            'PC2': {
                'ip': '************0',
                'netmask': '*************',
                'gateway': '************',
                'dns': '*******',
                'vlan': 20,
                'config_commands': [
                    "# PC2 网络配置",
                    "# IP地址: ************0",
                    "# 子网掩码: *************",
                    "# 默认网关: ************", 
                    "# DNS服务器: *******",
                    "",
                    "# Windows命令行配置:",
                    "netsh interface ip set address \"本地连接\" static ************0 ************* ************",
                    "netsh interface ip set dns \"本地连接\" static *******",
                    "",
                    "# Linux命令行配置:",
                    "# ifconfig eth0 ************0 netmask *************",
                    "# route add default gw ************",
                    "# echo 'nameserver *******' > /etc/resolv.conf"
                ]
            }
        }
        
        self.config['device_configs']['PC1'] = pc_configs['PC1']
        self.config['device_configs']['PC2'] = pc_configs['PC2']
        
        return pc_configs
    
    def generate_ip_plan(self):
        """生成IP地址规划"""
        ip_plan = {
            'vlan_10': {
                'network': '************/24',
                'gateway': '************',
                'usable_range': '************ - ************54',
                'assigned_ips': {
                    'AR1_subif': '************',
                    'PC1': '************0'
                }
            },
            'vlan_20': {
                'network': '************/24', 
                'gateway': '************',
                'usable_range': '************ - ************54',
                'assigned_ips': {
                    'AR1_subif': '************',
                    'PC2': '************0'
                }
            }
        }
        
        self.config['ip_plan'] = ip_plan
        return ip_plan
    
    def generate_all_configs(self):
        """生成所有配置"""
        print("=== 单臂路由配置生成器 ===")
        
        # 加载拓扑数据
        if not self.load_topology_data():
            print("无法加载拓扑数据，使用默认配置")
        
        # 生成各设备配置
        print("正在生成路由器配置...")
        self.generate_router_config()
        
        print("正在生成LSW1交换机配置...")
        self.generate_switch_lsw1_config()
        
        print("正在生成LSW2交换机配置...")
        self.generate_switch_lsw2_config()
        
        print("正在生成PC配置...")
        self.generate_pc_config()
        
        print("正在生成IP地址规划...")
        self.generate_ip_plan()
        
        # 设置VLAN配置
        self.config['vlan_config'] = self.vlan_plan
        
        return self.config
    
    def save_configs_to_files(self):
        """保存配置到文件"""
        try:
            # 创建配置文件目录
            config_dir = "single_arm_routing_configs"
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            # 保存完整配置JSON
            with open(os.path.join(config_dir, "complete_config.json"), 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            
            # 保存各设备的配置文件
            for device_name, device_config in self.config['device_configs'].items():
                if 'config_lines' in device_config:
                    filename = f"{device_name.lower()}_config.txt"
                    with open(os.path.join(config_dir, filename), 'w', encoding='utf-8') as f:
                        f.write('\n'.join(device_config['config_lines']))
            
            print(f"✅ 配置文件已保存到 {config_dir} 目录")
            return True
            
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
            return False
    
    def print_summary(self):
        """打印配置摘要"""
        print("\n" + "="*60)
        print("单臂路由配置摘要".center(60))
        print("="*60)
        
        print(f"\n📅 配置生成时间: {self.config['generated_time']}")
        
        print(f"\n🌐 网络拓扑:")
        print(f"   AR1 (路由器) ← Trunk → LSW1 (交换机) ← Trunk → LSW2 (交换机)")
        print(f"                                                    ├─ PC1 (VLAN10)")
        print(f"                                                    └─ PC2 (VLAN20)")
        
        print(f"\n📋 VLAN规划:")
        for vlan_id, vlan_info in self.config['vlan_config'].items():
            print(f"   {vlan_id.upper()}: {vlan_info['network']} - {vlan_info['description']}")
        
        print(f"\n🔧 设备配置:")
        for device_name, device_config in self.config['device_configs'].items():
            device_type = device_config.get('device_type', 'unknown')
            model = device_config.get('model', 'N/A')
            print(f"   {device_name} ({model}) - {device_type}")
        
        print(f"\n🌍 IP地址分配:")
        for vlan, ip_info in self.config['ip_plan'].items():
            print(f"   {vlan.upper()}: {ip_info['network']}")
            for device, ip in ip_info['assigned_ips'].items():
                print(f"     - {device}: {ip}")
        
        print(f"\n📁 生成的配置文件:")
        config_dir = "single_arm_routing_configs"
        if os.path.exists(config_dir):
            for file in os.listdir(config_dir):
                file_path = os.path.join(config_dir, file)
                if os.path.isfile(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"   ✅ {file} ({file_size} 字节)")

def main():
    """主函数"""
    generator = SingleArmRoutingConfig()
    
    # 生成所有配置
    config = generator.generate_all_configs()
    
    # 保存配置文件
    generator.save_configs_to_files()
    
    # 打印摘要
    generator.print_summary()
    
    print(f"\n🎉 单臂路由配置生成完成!")
    print(f"📋 详细配置请查看 single_arm_routing_configs 目录中的文件")

if __name__ == "__main__":
    main()
