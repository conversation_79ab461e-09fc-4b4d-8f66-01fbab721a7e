{"generated_time": "2025-06-01T06:23:21.540261", "topology_info": {"version": "1.3.00.100", "devices": {"B973542B-6760-40cb-A1A1-5B9258728B3B": {"id": "B973542B-6760-40cb-A1A1-5B9258728B3B", "name": "AR1", "model": "AR201", "system_mac": "00-E0-FC-3F-08-F0", "com_port": "2000", "poe": false, "bootmode": "0", "position": {"x": 198.0, "y": 76.0, "edit_left": 225, "edit_top": 130}, "interfaces": [{"number": "slot17", "is_main_board": true, "interfaces": [{"type": "Ethernet", "name": "Ethernet", "count": 8}, {"type": "Ethernet", "name": "Ethernet", "count": 1}]}], "settings": ""}, "CAD83C05-670C-4ef5-A165-6F7BCB8BBA61": {"id": "CAD83C05-670C-4ef5-A165-6F7BCB8BBA61", "name": "LSW1", "model": "S5700", "system_mac": "4C-1F-CC-FE-67-9B", "com_port": "2001", "poe": false, "bootmode": "0", "position": {"x": 205.0, "y": 223.0, "edit_left": 232, "edit_top": 277}, "interfaces": [{"number": "slot17", "is_main_board": true, "interfaces": [{"type": "Ethernet", "name": "GE", "count": 24}]}], "settings": ""}, "C1548A47-90F6-42ac-AE86-F7AB4B9B73C2": {"id": "C1548A47-90F6-42ac-AE86-F7AB4B9B73C2", "name": "PC1", "model": "PC", "system_mac": "54-89-98-8C-16-F1", "com_port": "0", "poe": false, "bootmode": "0", "position": {"x": 119.0, "y": 461.0, "edit_left": 146, "edit_top": 515}, "interfaces": [{"number": "slot17", "is_main_board": true, "interfaces": [{"type": "Ethernet", "name": "Ethernet", "count": 1}]}], "settings": " -simpc_ip 0.0.0.0  -simpc_mask 0.0.0.0  -simpc_gateway 0.0.0.0  -simpc_mac 54-89-98-8C-16-F1  -simpc_mc_dstip 0.0.0.0  -simpc_mc_dstmac 00-00-00-00-00-00  -simpc_dns1 0.0.0.0  -simpc_dns2 0.0.0.0  -simpc_ipv6 ::  -simpc_prefix 128  -simpc_gatewayv6 ::  -simpc_dhcp_state 0  -simpc_dhcpv6_state 0  -simpc_dns_auto_state 0  -simpc_igmp_version 1  -simpc_group_ip_start 0.0.0.0  -simpc_src_ip_start 0.0.0.0  -simpc_group_num 0  -simpc_group_step 0  -simpc_src_num 0  -simpc_src_step 0  -simpc_type MODE_IS_INCLUDE ", "pc_config": {"simpc_ip": "0.0.0.0", "simpc_mask": "0.0.0.0", "simpc_gateway": "0.0.0.0", "simpc_mac": "54-89-98-8C-16-F1", "simpc_mc_dstip": "0.0.0.0", "simpc_mc_dstmac": "00-00-00-00-00-00", "simpc_dns1": "0.0.0.0", "simpc_dns2": "0.0.0.0", "simpc_ipv6": "::", "simpc_prefix": "128", "simpc_gatewayv6": "::", "simpc_dhcp_state": "0", "simpc_dhcpv6_state": "0", "simpc_dns_auto_state": "0", "simpc_igmp_version": "1", "simpc_group_ip_start": "0.0.0.0", "simpc_src_ip_start": "0.0.0.0", "simpc_group_num": "0", "simpc_group_step": "0", "simpc_src_num": "0", "simpc_src_step": "0", "simpc_type": "MODE_IS_INCLUDE"}}, "D4B27BF6-D56E-4a1f-9259-9D668744CD58": {"id": "D4B27BF6-D56E-4a1f-9259-9D668744CD58", "name": "PC2", "model": "PC", "system_mac": "54-89-98-71-50-C0", "com_port": "0", "poe": false, "bootmode": "0", "position": {"x": 314.0, "y": 473.0, "edit_left": 341, "edit_top": 527}, "interfaces": [{"number": "slot17", "is_main_board": true, "interfaces": [{"type": "Ethernet", "name": "Ethernet", "count": 1}]}], "settings": " -simpc_ip 0.0.0.0  -simpc_mask 0.0.0.0  -simpc_gateway 0.0.0.0  -simpc_mac 54-89-98-71-50-C0  -simpc_mc_dstip 0.0.0.0  -simpc_mc_dstmac 00-00-00-00-00-00  -simpc_dns1 0.0.0.0  -simpc_dns2 0.0.0.0  -simpc_ipv6 ::  -simpc_prefix 128  -simpc_gatewayv6 ::  -simpc_dhcp_state 0  -simpc_dhcpv6_state 0  -simpc_dns_auto_state 0  -simpc_igmp_version 1  -simpc_group_ip_start 0.0.0.0  -simpc_src_ip_start 0.0.0.0  -simpc_group_num 0  -simpc_group_step 0  -simpc_src_num 0  -simpc_src_step 0  -simpc_type MODE_IS_INCLUDE ", "pc_config": {"simpc_ip": "0.0.0.0", "simpc_mask": "0.0.0.0", "simpc_gateway": "0.0.0.0", "simpc_mac": "54-89-98-71-50-C0", "simpc_mc_dstip": "0.0.0.0", "simpc_mc_dstmac": "00-00-00-00-00-00", "simpc_dns1": "0.0.0.0", "simpc_dns2": "0.0.0.0", "simpc_ipv6": "::", "simpc_prefix": "128", "simpc_gatewayv6": "::", "simpc_dhcp_state": "0", "simpc_dhcpv6_state": "0", "simpc_dns_auto_state": "0", "simpc_igmp_version": "1", "simpc_group_ip_start": "0.0.0.0", "simpc_src_ip_start": "0.0.0.0", "simpc_group_num": "0", "simpc_group_step": "0", "simpc_src_num": "0", "simpc_src_step": "0", "simpc_type": "MODE_IS_INCLUDE"}}, "B9407E96-851C-4284-B28C-819E7F714866": {"id": "B9407E96-851C-4284-B28C-819E7F714866", "name": "LSW2", "model": "S3700", "system_mac": "4C-1F-CC-E7-06-D8", "com_port": "2002", "poe": false, "bootmode": "0", "position": {"x": 213.0, "y": 358.0, "edit_left": 240, "edit_top": 412}, "interfaces": [{"number": "slot17", "is_main_board": true, "interfaces": [{"type": "Ethernet", "name": "Ethernet", "count": 22}, {"type": "Ethernet", "name": "GE", "count": 2}]}], "settings": ""}}, "connections": [{"src_device_id": "B973542B-6760-40cb-A1A1-5B9258728B3B", "dest_device_id": "CAD83C05-670C-4ef5-A165-6F7BCB8BBA61", "interface_pairs": [{"line_name": "Copper", "src_index": 0, "dest_index": 0, "src_position": {"x": 227.054047, "y": 146.134888}, "dest_position": {"x": 229.945953, "y": 206.865112}}]}, {"src_device_id": "CAD83C05-670C-4ef5-A165-6F7BCB8BBA61", "dest_device_id": "B9407E96-851C-4284-B28C-819E7F714866", "interface_pairs": [{"line_name": "Copper", "src_index": 1, "dest_index": 0, "src_position": {"x": 234.55455, "y": 293.108154}, "dest_position": {"x": 237.44545, "y": 341.891846}}]}, {"src_device_id": "B9407E96-851C-4284-B28C-819E7F714866", "dest_device_id": "C1548A47-90F6-42ac-AE86-F7AB4B9B73C2", "interface_pairs": [{"line_name": "Copper", "src_index": 1, "dest_index": 0, "src_position": {"x": 210.889847, "y": 416.897278}, "dest_position": {"x": 175.110153, "y": 456.102722}}]}, {"src_device_id": "B9407E96-851C-4284-B28C-819E7F714866", "dest_device_id": "D4B27BF6-D56E-4a1f-9259-9D668744CD58", "interface_pairs": [{"line_name": "Copper", "src_index": 2, "dest_index": 0, "src_position": {"x": 268.496582, "y": 417.446594}, "dest_position": {"x": 312.503418, "y": 467.553406}}]}], "device_count": 5, "connection_count": 4}, "vlan_config": {"vlan10": {"name": "VLAN_PC1", "network": "************/24", "gateway": "************", "description": "PC1网段"}, "vlan20": {"name": "VLAN_PC2", "network": "************/24", "gateway": "************", "description": "PC2网段"}}, "device_configs": {"AR1": {"device_type": "router", "model": "AR201", "config_lines": ["# AR1 (AR201) 单臂路由配置", "# 配置生成时间: 2025-06-01 06:23:21", "", "# 系统配置", "sysname AR1", "", "# 配置子接口实现单臂路由", "interface Ethernet0/0/0", " description 连接到LSW1-GE0/0/0", " undo shutdown", "", "# VLAN 10 子接口配置 (PC1网段)", "interface Ethernet0/0/0.10", " description VLAN10-PC1网段", " dot1q termination vid 10", " ip address ************ *************", " arp broadcast enable", "", "# VLAN 20 子接口配置 (PC2网段)", "interface Ethernet0/0/0.20", " description VLAN20-PC2网段", " dot1q termination vid 20", " ip address ************ *************", " arp broadcast enable", "", "# 配置默认路由(如果需要外网访问)", "# ip route-static 0.0.0.0 0.0.0.0 <外网网关IP>", "", "# 保存配置", "save", "quit"], "interfaces": {"Ethernet0/0/0": {"description": "连接到LSW1", "type": "trunk", "vlans": [10, 20]}, "Ethernet0/0/0.10": {"description": "VLAN10子接口", "ip": "************/24", "vlan": 10}, "Ethernet0/0/0.20": {"description": "VLAN20子接口", "ip": "************/24", "vlan": 20}}}, "LSW1": {"device_type": "switch", "model": "S5700", "config_lines": ["# LSW1 (S5700) 配置", "# 配置生成时间: 2025-06-01 06:23:21", "", "# 系统配置", "sysname LSW1", "", "# 创建VLAN", "vlan batch 10 20", "", "vlan 10", " description PC1网段", " quit", "", "vlan 20", " description PC2网段", " quit", "", "# 配置连接路由器的接口为Trunk", "interface GigabitEthernet0/0/1", " description 连接到AR1-Ethernet0/0/0", " port link-type trunk", " port trunk allow-pass vlan 10 20", " undo shutdown", "", "# 配置连接LSW2的接口为Trunk", "interface GigabitEthernet0/0/2", " description 连接到LSW2-GE0/0/1", " port link-type trunk", " port trunk allow-pass vlan 10 20", " undo shutdown", "", "# 配置STP防止环路", "stp enable", "stp mode rstp", "", "# 保存配置", "save", "quit"], "vlans": [10, 20], "interfaces": {"GigabitEthernet0/0/1": {"description": "连接到AR1", "type": "trunk", "vlans": [10, 20]}, "GigabitEthernet0/0/2": {"description": "连接到LSW2", "type": "trunk", "vlans": [10, 20]}}}, "LSW2": {"device_type": "switch", "model": "S3700", "config_lines": ["# LSW2 (S3700) 配置", "# 配置生成时间: 2025-06-01 06:23:21", "", "# 系统配置", "sysname LSW2", "", "# 创建VLAN", "vlan batch 10 20", "", "vlan 10", " description PC1网段", " quit", "", "vlan 20", " description PC2网段", " quit", "", "# 配置连接LSW1的接口为Trunk", "interface GigabitEthernet0/0/1", " description 连接到LSW1-GE0/0/2", " port link-type trunk", " port trunk allow-pass vlan 10 20", " undo shutdown", "", "# 配置连接PC1的接口为Access VLAN 10", "interface Ethernet0/0/1", " description 连接到PC1", " port link-type access", " port default vlan 10", " undo shutdown", "", "# 配置连接PC2的接口为Access VLAN 20", "interface Ethernet0/0/2", " description 连接到PC2", " port link-type access", " port default vlan 20", " undo shutdown", "", "# 配置STP防止环路", "stp enable", "stp mode rstp", "", "# 保存配置", "save", "quit"], "vlans": [10, 20], "interfaces": {"GigabitEthernet0/0/1": {"description": "连接到LSW1", "type": "trunk", "vlans": [10, 20]}, "Ethernet0/0/1": {"description": "连接到PC1", "type": "access", "vlan": 10}, "Ethernet0/0/2": {"description": "连接到PC2", "type": "access", "vlan": 20}}}, "PC1": {"ip": "************0", "netmask": "*************", "gateway": "************", "dns": "*******", "vlan": 10, "config_commands": ["# PC1 网络配置", "# IP地址: ************0", "# 子网掩码: *************", "# 默认网关: ************", "# DNS服务器: *******", "", "# Windows命令行配置:", "netsh interface ip set address \"本地连接\" static ************0 ************* ************", "netsh interface ip set dns \"本地连接\" static *******", "", "# Linux命令行配置:", "# ifconfig eth0 ************0 netmask *************", "# route add default gw ************", "# echo 'nameserver *******' > /etc/resolv.conf"]}, "PC2": {"ip": "************0", "netmask": "*************", "gateway": "************", "dns": "*******", "vlan": 20, "config_commands": ["# PC2 网络配置", "# IP地址: ************0", "# 子网掩码: *************", "# 默认网关: ************", "# DNS服务器: *******", "", "# Windows命令行配置:", "netsh interface ip set address \"本地连接\" static ************0 ************* ************", "netsh interface ip set dns \"本地连接\" static *******", "", "# Linux命令行配置:", "# ifconfig eth0 ************0 netmask *************", "# route add default gw ************", "# echo 'nameserver *******' > /etc/resolv.conf"]}}, "ip_plan": {"vlan_10": {"network": "************/24", "gateway": "************", "usable_range": "************ - ************54", "assigned_ips": {"AR1_subif": "************", "PC1": "************0"}}, "vlan_20": {"network": "************/24", "gateway": "************", "usable_range": "************ - ************54", "assigned_ips": {"AR1_subif": "************", "PC2": "************0"}}}}