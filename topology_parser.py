#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
eNSP拓扑图文件与efz文档解析器
用于读取和解析eNSP网络仿真平台的拓扑文件和设备配置文件
"""

import xml.etree.ElementTree as ET
import os
import zipfile
import struct
import json
from typing import Dict, List, Any, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TopoParser:
    """拓扑文件解析器"""
    
    def __init__(self, topo_file: str):
        self.topo_file = topo_file
        self.devices = {}
        self.connections = []
        
    def parse(self) -> Dict[str, Any]:
        """解析拓扑文件"""
        try:
            tree = ET.parse(self.topo_file)
            root = tree.getroot()
            
            # 解析设备信息
            devices_element = root.find('devices')
            if devices_element is not None:
                for dev in devices_element.findall('dev'):
                    device_info = self._parse_device(dev)
                    self.devices[device_info['id']] = device_info
            
            # 解析连接信息
            lines_element = root.find('lines')
            if lines_element is not None:
                for line in lines_element.findall('line'):
                    connection_info = self._parse_connection(line)
                    self.connections.append(connection_info)
            
            return {
                'version': root.get('version', ''),
                'devices': self.devices,
                'connections': self.connections
            }
            
        except Exception as e:
            logger.error(f"解析拓扑文件失败: {e}")
            return {}
    
    def _parse_device(self, dev_element) -> Dict[str, Any]:
        """解析单个设备信息"""
        device = {
            'id': dev_element.get('id', ''),
            'name': dev_element.get('name', ''),
            'model': dev_element.get('model', ''),
            'system_mac': dev_element.get('system_mac', ''),
            'com_port': dev_element.get('com_port', ''),
            'position': {
                'x': float(dev_element.get('cx', 0)),
                'y': float(dev_element.get('cy', 0))
            },
            'interfaces': []
        }
        
        # 解析接口信息
        for slot in dev_element.findall('slot'):
            for interface in slot.findall('interface'):
                interface_info = {
                    'type': interface.get('sztype', ''),
                    'name': interface.get('interfacename', ''),
                    'count': int(interface.get('count', 0))
                }
                device['interfaces'].append(interface_info)
        
        # 解析PC设备的特殊配置
        if device['model'] == 'PC':
            settings = dev_element.get('settings', '')
            device['pc_config'] = self._parse_pc_settings(settings)
        
        return device
    
    def _parse_pc_settings(self, settings: str) -> Dict[str, str]:
        """解析PC设备的配置参数"""
        config = {}
        if settings:
            # 简单的参数解析
            parts = settings.split(' -')
            for part in parts:
                if part.strip():
                    if ' ' in part:
                        key, value = part.split(' ', 1)
                        config[key] = value
        return config
    
    def _parse_connection(self, line_element) -> Dict[str, Any]:
        """解析连接信息"""
        connection = {
            'src_device': line_element.get('srcDeviceID', ''),
            'dest_device': line_element.get('destDeviceID', ''),
            'interfaces': []
        }
        
        for interface_pair in line_element.findall('interfacePair'):
            interface_info = {
                'line_type': interface_pair.get('lineName', ''),
                'src_index': int(interface_pair.get('srcIndex', 0)),
                'dest_index': int(interface_pair.get('tarIndex', 0))
            }
            connection['interfaces'].append(interface_info)
        
        return connection

class EfzParser:
    """EFZ文件解析器"""
    
    def __init__(self, efz_file: str):
        self.efz_file = efz_file
        
    def parse(self) -> Dict[str, Any]:
        """解析EFZ文件"""
        try:
            with open(self.efz_file, 'rb') as f:
                data = f.read()
            
            # EFZ文件头部分析
            if not data.startswith(b'eNSP'):
                logger.warning(f"文件 {self.efz_file} 不是有效的EFZ格式")
                return {}
            
            result = {
                'file_type': 'eNSP EFZ',
                'file_size': len(data),
                'header': self._parse_header(data[:100]),
                'content_info': self._analyze_content(data)
            }
            
            # 尝试提取配置信息
            config_data = self._extract_config(data)
            if config_data:
                result['configuration'] = config_data
            
            return result
            
        except Exception as e:
            logger.error(f"解析EFZ文件失败: {e}")
            return {}
    
    def _parse_header(self, header_data: bytes) -> Dict[str, Any]:
        """解析文件头"""
        header = {
            'signature': header_data[:4].decode('ascii', errors='ignore'),
            'raw_header': header_data.hex()
        }
        return header
    
    def _analyze_content(self, data: bytes) -> Dict[str, Any]:
        """分析文件内容"""
        content_info = {
            'total_size': len(data),
            'contains_vrpcfg': b'vrpcfg.zip' in data,
            'contains_src': b'src' in data,
            'contains_rr_dat': b'rr.dat' in data,
            'text_content': []
        }
        
        # 查找可读文本内容
        try:
            text_parts = []
            current_text = ""
            for byte in data:
                if 32 <= byte <= 126:  # 可打印ASCII字符
                    current_text += chr(byte)
                else:
                    if len(current_text) > 3:  # 只保留长度大于3的文本
                        text_parts.append(current_text)
                    current_text = ""
            
            if current_text and len(current_text) > 3:
                text_parts.append(current_text)
            
            content_info['text_content'] = text_parts[:10]  # 只保留前10个文本片段
            
        except Exception as e:
            logger.warning(f"提取文本内容失败: {e}")
        
        return content_info
    
    def _extract_config(self, data: bytes) -> Optional[Dict[str, Any]]:
        """尝试提取配置信息"""
        config = {}
        
        # 查找vrpcfg.zip引用
        if b'vrpcfg.zip' in data:
            config['has_vrp_config'] = True
            
        # 查找其他配置文件引用
        config_files = [b'rr.dat', b'rr.bak', b'compatible', b'patchfile.src']
        for config_file in config_files:
            if config_file in data:
                config[config_file.decode('ascii')] = True
        
        return config if config else None

def main():
    """主函数"""
    print("=== eNSP拓扑图文件与EFZ文档解析器 ===\n")

    # 解析拓扑文件
    topo_file = "1.topo"
    if os.path.exists(topo_file):
        print(f"正在解析拓扑文件: {topo_file}")
        topo_parser = TopoParser(topo_file)
        topo_result = topo_parser.parse()

        if topo_result:
            print(f"\n拓扑文件解析结果:")
            print(f"版本: {topo_result.get('version', 'N/A')}")
            print(f"设备数量: {len(topo_result.get('devices', {}))}")
            print(f"连接数量: {len(topo_result.get('connections', []))}")

            print("\n设备列表:")
            for device_id, device in topo_result.get('devices', {}).items():
                print(f"  - {device['name']} ({device['model']}) - MAC: {device['system_mac']}")
                if device['model'] == 'PC' and 'pc_config' in device:
                    print(f"    PC配置参数数量: {len(device['pc_config'])}")

            print("\n连接关系:")
            for i, conn in enumerate(topo_result.get('connections', []), 1):
                src_name = topo_result['devices'].get(conn['src_device'], {}).get('name', 'Unknown')
                dest_name = topo_result['devices'].get(conn['dest_device'], {}).get('name', 'Unknown')
                print(f"  {i}. {src_name} <-> {dest_name}")
        else:
            print("拓扑文件解析失败")
    else:
        print(f"拓扑文件 {topo_file} 不存在")

    # 解析EFZ文件
    print("\n" + "="*50)
    efz_dirs = [d for d in os.listdir('.') if os.path.isdir(d) and os.path.exists(os.path.join(d, 'flash.efz'))]

    for efz_dir in efz_dirs:
        efz_file = os.path.join(efz_dir, 'flash.efz')
        print(f"\n正在解析EFZ文件: {efz_file}")

        efz_parser = EfzParser(efz_file)
        efz_result = efz_parser.parse()

        if efz_result:
            print(f"文件类型: {efz_result.get('file_type', 'N/A')}")
            print(f"文件大小: {efz_result.get('file_size', 0)} 字节")

            content_info = efz_result.get('content_info', {})
            print(f"包含vrpcfg.zip: {content_info.get('contains_vrpcfg', False)}")
            print(f"包含src: {content_info.get('contains_src', False)}")
            print(f"包含rr.dat: {content_info.get('contains_rr_dat', False)}")

            if 'configuration' in efz_result:
                config = efz_result['configuration']
                print("配置文件:")
                for key, value in config.items():
                    if value:
                        print(f"  - {key}")

            text_content = content_info.get('text_content', [])
            if text_content:
                print("发现的文本内容片段:")
                for i, text in enumerate(text_content[:5], 1):  # 只显示前5个
                    print(f"  {i}. {text[:50]}...")
        else:
            print("EFZ文件解析失败")

    print("\n解析完成!")

if __name__ == "__main__":
    main()
