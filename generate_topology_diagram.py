#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成网络拓扑图的可视化脚本
基于解析结果生成Mermaid图表
"""

import json
import os

def load_parse_results(json_file: str = "ensp_parse_results.json"):
    """加载解析结果"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载解析结果失败: {e}")
        return None

def generate_mermaid_diagram(results: dict) -> str:
    """生成Mermaid网络拓扑图"""
    if not results or 'topology' not in results:
        return ""
    
    topology = results['topology']
    devices = topology.get('devices', {})
    connections = topology.get('connections', [])
    
    # 开始构建Mermaid图表
    mermaid_lines = [
        "graph TD",
        "    %% eNSP网络拓扑图",
        f"    %% 版本: {topology.get('version', 'N/A')}",
        f"    %% 设备数: {len(devices)}, 连接数: {len(connections)}",
        ""
    ]
    
    # 定义设备节点
    device_styles = {
        'AR201': ('router', '🔀'),
        'S5700': ('switch', '🔄'),
        'S3700': ('switch', '🔄'),
        'PC': ('pc', '💻')
    }
    
    mermaid_lines.append("    %% 设备定义")
    for device_id, device in devices.items():
        device_name = device['name']
        device_model = device['model']
        
        # 获取设备样式
        style_info = device_styles.get(device_model, ('device', '📱'))
        icon = style_info[1]
        
        # 创建节点
        node_label = f"{icon} {device_name}<br/>({device_model})"
        mermaid_lines.append(f"    {device_name}[\"{node_label}\"]")
    
    mermaid_lines.append("")
    
    # 定义连接
    mermaid_lines.append("    %% 连接定义")
    for i, connection in enumerate(connections):
        src_device_id = connection['src_device_id']
        dest_device_id = connection['dest_device_id']
        
        # 查找设备名称
        src_name = devices.get(src_device_id, {}).get('name', f'Device_{i}_src')
        dest_name = devices.get(dest_device_id, {}).get('name', f'Device_{i}_dest')
        
        # 获取接口信息
        interface_pairs = connection.get('interface_pairs', [])
        if interface_pairs:
            pair = interface_pairs[0]  # 取第一个接口对
            line_type = pair.get('line_name', 'Unknown')
            src_index = pair.get('src_index', 0)
            dest_index = pair.get('dest_index', 0)
            
            # 创建连接线
            connection_label = f"{line_type}<br/>({src_index}↔{dest_index})"
            mermaid_lines.append(f"    {src_name} ---|{connection_label}| {dest_name}")
        else:
            mermaid_lines.append(f"    {src_name} --- {dest_name}")
    
    mermaid_lines.append("")
    
    # 添加样式定义
    mermaid_lines.extend([
        "    %% 样式定义",
        "    classDef router fill:#ff9999,stroke:#333,stroke-width:2px",
        "    classDef switch fill:#99ccff,stroke:#333,stroke-width:2px", 
        "    classDef pc fill:#99ff99,stroke:#333,stroke-width:2px",
        ""
    ])
    
    # 应用样式
    for device_id, device in devices.items():
        device_name = device['name']
        device_model = device['model']
        
        if 'AR' in device_model:
            mermaid_lines.append(f"    class {device_name} router")
        elif 'S' in device_model:
            mermaid_lines.append(f"    class {device_name} switch")
        elif device_model == 'PC':
            mermaid_lines.append(f"    class {device_name} pc")
    
    return "\n".join(mermaid_lines)

def generate_device_details_table(results: dict) -> str:
    """生成设备详情表格"""
    if not results or 'topology' not in results:
        return ""
    
    devices = results['topology'].get('devices', {})
    
    table_lines = [
        "| 设备名称 | 型号 | MAC地址 | 通信端口 | 接口数量 | 位置坐标 |",
        "|----------|------|---------|----------|----------|----------|"
    ]
    
    for device in devices.values():
        name = device['name']
        model = device['model']
        mac = device['system_mac']
        port = device['com_port']
        
        # 计算接口总数
        total_interfaces = 0
        for slot in device.get('interfaces', []):
            for interface in slot.get('interfaces', []):
                total_interfaces += interface.get('count', 0)
        
        position = device.get('position', {})
        pos_str = f"({position.get('x', 0):.0f}, {position.get('y', 0):.0f})"
        
        table_lines.append(f"| {name} | {model} | {mac} | {port} | {total_interfaces} | {pos_str} |")
    
    return "\n".join(table_lines)

def generate_config_summary(results: dict) -> str:
    """生成配置摘要"""
    if not results:
        return ""
    
    summary = results.get('summary', {})
    devices_config = results.get('devices_config', {})
    
    summary_lines = [
        "## 配置解析摘要",
        "",
        f"- **拓扑版本**: {summary.get('topology_version', 'N/A')}",
        f"- **设备总数**: {summary.get('total_devices', 0)}",
        f"- **连接总数**: {summary.get('total_connections', 0)}",
        f"- **EFZ文件**: {summary.get('efz_files_parsed', 0)}",
        "",
        "### 设备类型分布",
        ""
    ]
    
    for device_type, count in summary.get('device_types', {}).items():
        summary_lines.append(f"- **{device_type}**: {count} 台")
    
    summary_lines.extend([
        "",
        "### 连接类型分布",
        ""
    ])
    
    for conn_type, count in summary.get('connection_types', {}).items():
        summary_lines.append(f"- **{conn_type}**: {count} 条")
    
    if devices_config:
        summary_lines.extend([
            "",
            "### EFZ配置文件分析",
            ""
        ])
        
        for device_id, config in devices_config.items():
            device_name = results['topology']['devices'].get(device_id, {}).get('name', device_id[:8])
            file_size = config.get('file_size', 0)
            config_files = config.get('config_files', [])
            
            summary_lines.append(f"**{device_name}**:")
            summary_lines.append(f"- 文件大小: {file_size} 字节")
            summary_lines.append(f"- 配置文件: {', '.join(config_files)}")
            summary_lines.append("")
    
    return "\n".join(summary_lines)

def main():
    """主函数"""
    print("=== 生成网络拓扑可视化图表 ===")
    
    # 加载解析结果
    results = load_parse_results()
    if not results:
        print("无法加载解析结果，请先运行 ensp_parser.py")
        return
    
    # 生成Mermaid图表
    mermaid_diagram = generate_mermaid_diagram(results)
    
    # 生成设备详情表格
    device_table = generate_device_details_table(results)
    
    # 生成配置摘要
    config_summary = generate_config_summary(results)
    
    # 生成完整的Markdown报告
    report_lines = [
        "# eNSP网络拓扑解析报告",
        "",
        f"**解析时间**: {results.get('parse_time', 'N/A')}",
        "",
        config_summary,
        "",
        "## 网络拓扑图",
        "",
        "```mermaid",
        mermaid_diagram,
        "```",
        "",
        "## 设备详情",
        "",
        device_table,
        "",
        "---",
        "*此报告由eNSP解析器自动生成*"
    ]
    
    # 保存报告
    report_content = "\n".join(report_lines)
    
    try:
        with open("topology_report.md", 'w', encoding='utf-8') as f:
            f.write(report_content)
        print("✓ 拓扑报告已保存到: topology_report.md")
        
        # 同时保存纯Mermaid图表
        with open("topology_diagram.mmd", 'w', encoding='utf-8') as f:
            f.write(mermaid_diagram)
        print("✓ Mermaid图表已保存到: topology_diagram.mmd")
        
    except Exception as e:
        print(f"✗ 保存报告失败: {e}")
    
    # 打印Mermaid图表到控制台
    print("\n=== Mermaid网络拓扑图 ===")
    print(mermaid_diagram)

if __name__ == "__main__":
    main()
