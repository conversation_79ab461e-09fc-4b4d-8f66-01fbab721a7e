#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
eNSP拓扑图文件与EFZ文档完整解析器
支持详细的配置信息提取和JSON输出
"""

import xml.etree.ElementTree as ET
import os
import json
import struct
from datetime import datetime
from typing import Dict, List, Any, Optional

class ENSPParser:
    """eNSP文件解析器主类"""
    
    def __init__(self, workspace_dir: str = "."):
        self.workspace_dir = workspace_dir
        self.result = {
            'parse_time': datetime.now().isoformat(),
            'workspace': workspace_dir,
            'topology': {},
            'devices_config': {},
            'summary': {}
        }
    
    def parse_all(self) -> Dict[str, Any]:
        """解析所有文件"""
        print("=== eNSP完整解析器 ===")
        
        # 解析拓扑文件
        self._parse_topology()
        
        # 解析EFZ配置文件
        self._parse_efz_files()
        
        # 生成摘要
        self._generate_summary()
        
        return self.result
    
    def _parse_topology(self):
        """解析拓扑文件"""
        topo_file = os.path.join(self.workspace_dir, "1.topo")
        
        if not os.path.exists(topo_file):
            print(f"警告: 拓扑文件 {topo_file} 不存在")
            return
        
        print(f"正在解析拓扑文件: {topo_file}")
        
        try:
            # 读取并处理编码
            with open(topo_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            content = content.replace('encoding="UNICODE"', 'encoding="UTF-8"')
            
            root = ET.fromstring(content)
            
            # 基本信息
            self.result['topology'] = {
                'version': root.get('version', ''),
                'devices': {},
                'connections': [],
                'device_count': 0,
                'connection_count': 0
            }
            
            # 解析设备
            devices_element = root.find('devices')
            if devices_element is not None:
                for dev in devices_element.findall('dev'):
                    device_info = self._parse_device_detailed(dev)
                    self.result['topology']['devices'][device_info['id']] = device_info
            
            # 解析连接
            lines_element = root.find('lines')
            if lines_element is not None:
                for line in lines_element.findall('line'):
                    connection_info = self._parse_connection_detailed(line)
                    self.result['topology']['connections'].append(connection_info)
            
            # 更新计数
            self.result['topology']['device_count'] = len(self.result['topology']['devices'])
            self.result['topology']['connection_count'] = len(self.result['topology']['connections'])
            
            print(f"  ✓ 解析完成: {self.result['topology']['device_count']} 设备, {self.result['topology']['connection_count']} 连接")
            
        except Exception as e:
            print(f"  ✗ 拓扑文件解析失败: {e}")
    
    def _parse_device_detailed(self, dev_element) -> Dict[str, Any]:
        """详细解析设备信息"""
        device = {
            'id': dev_element.get('id', ''),
            'name': dev_element.get('name', ''),
            'model': dev_element.get('model', ''),
            'system_mac': dev_element.get('system_mac', ''),
            'com_port': dev_element.get('com_port', ''),
            'poe': dev_element.get('poe', '0') == '1',
            'bootmode': dev_element.get('bootmode', '0'),
            'position': {
                'x': float(dev_element.get('cx', 0)),
                'y': float(dev_element.get('cy', 0)),
                'edit_left': int(dev_element.get('edit_left', 0)),
                'edit_top': int(dev_element.get('edit_top', 0))
            },
            'interfaces': [],
            'settings': dev_element.get('settings', '')
        }
        
        # 解析接口信息
        for slot in dev_element.findall('slot'):
            slot_info = {
                'number': slot.get('number', ''),
                'is_main_board': slot.get('isMainBoard', '0') == '1',
                'interfaces': []
            }
            
            for interface in slot.findall('interface'):
                interface_info = {
                    'type': interface.get('sztype', ''),
                    'name': interface.get('interfacename', ''),
                    'count': int(interface.get('count', 0))
                }
                slot_info['interfaces'].append(interface_info)
            
            device['interfaces'].append(slot_info)
        
        # 解析PC特殊配置
        if device['model'] == 'PC' and device['settings']:
            device['pc_config'] = self._parse_pc_config(device['settings'])
        
        return device
    
    def _parse_pc_config(self, settings: str) -> Dict[str, str]:
        """解析PC配置"""
        config = {}
        if settings.strip():
            # 分割参数
            parts = settings.split(' -')
            for part in parts:
                part = part.strip()
                if part and ' ' in part:
                    key, value = part.split(' ', 1)
                    config[key] = value.strip()
        return config
    
    def _parse_connection_detailed(self, line_element) -> Dict[str, Any]:
        """详细解析连接信息"""
        connection = {
            'src_device_id': line_element.get('srcDeviceID', ''),
            'dest_device_id': line_element.get('destDeviceID', ''),
            'interface_pairs': []
        }
        
        for interface_pair in line_element.findall('interfacePair'):
            pair_info = {
                'line_name': interface_pair.get('lineName', ''),
                'src_index': int(interface_pair.get('srcIndex', 0)),
                'dest_index': int(interface_pair.get('tarIndex', 0)),
                'src_position': {
                    'x': float(interface_pair.get('srcBoundRect_X', 0)),
                    'y': float(interface_pair.get('srcBoundRect_Y', 0))
                },
                'dest_position': {
                    'x': float(interface_pair.get('tarBoundRect_X', 0)),
                    'y': float(interface_pair.get('tarBoundRect_Y', 0))
                }
            }
            connection['interface_pairs'].append(pair_info)
        
        return connection
    
    def _parse_efz_files(self):
        """解析所有EFZ文件"""
        print("\n正在解析EFZ配置文件...")
        
        # 查找所有EFZ文件
        efz_files = []
        for item in os.listdir(self.workspace_dir):
            item_path = os.path.join(self.workspace_dir, item)
            if os.path.isdir(item_path):
                efz_path = os.path.join(item_path, 'flash.efz')
                if os.path.exists(efz_path):
                    efz_files.append((item, efz_path))
        
        for device_id, efz_path in efz_files:
            print(f"  解析设备 {device_id} 的配置文件...")
            config_info = self._parse_efz_file(efz_path)
            self.result['devices_config'][device_id] = config_info
        
        print(f"  ✓ 完成 {len(efz_files)} 个EFZ文件解析")
    
    def _parse_efz_file(self, efz_path: str) -> Dict[str, Any]:
        """解析单个EFZ文件"""
        try:
            with open(efz_path, 'rb') as f:
                data = f.read()
            
            config = {
                'file_path': efz_path,
                'file_size': len(data),
                'is_valid_efz': data.startswith(b'eNSP'),
                'header_info': {},
                'content_analysis': {},
                'config_files': []
            }
            
            if config['is_valid_efz']:
                # 解析文件头
                config['header_info'] = {
                    'signature': data[:4].decode('ascii', errors='ignore'),
                    'header_hex': data[:32].hex()
                }
                
                # 内容分析
                config_keywords = [
                    b'vrpcfg.zip', b'src', b'rr.dat', b'rr.bak', 
                    b'compatible', b'patchfile.src', b'patchfilenext.src'
                ]
                
                found_configs = []
                for keyword in config_keywords:
                    if keyword in data:
                        found_configs.append(keyword.decode('ascii'))
                
                config['config_files'] = found_configs
                
                # 提取可读文本
                text_content = self._extract_readable_text(data)
                config['content_analysis'] = {
                    'readable_text_count': len(text_content),
                    'sample_text': text_content[:3] if text_content else []
                }
            
            return config
            
        except Exception as e:
            return {
                'file_path': efz_path,
                'error': str(e),
                'is_valid_efz': False
            }
    
    def _extract_readable_text(self, data: bytes) -> List[str]:
        """提取可读文本内容"""
        text_parts = []
        current_text = ""
        
        for byte in data:
            if 32 <= byte <= 126:  # 可打印ASCII字符
                current_text += chr(byte)
            else:
                if len(current_text) > 5:  # 只保留长度大于5的文本
                    text_parts.append(current_text)
                current_text = ""
        
        if current_text and len(current_text) > 5:
            text_parts.append(current_text)
        
        return text_parts[:20]  # 最多返回20个文本片段
    
    def _generate_summary(self):
        """生成解析摘要"""
        topology = self.result.get('topology', {})
        devices_config = self.result.get('devices_config', {})
        
        # 设备类型统计
        device_types = {}
        for device in topology.get('devices', {}).values():
            model = device.get('model', 'Unknown')
            device_types[model] = device_types.get(model, 0) + 1
        
        # 连接类型统计
        connection_types = {}
        for conn in topology.get('connections', []):
            for pair in conn.get('interface_pairs', []):
                line_type = pair.get('line_name', 'Unknown')
                connection_types[line_type] = connection_types.get(line_type, 0) + 1
        
        self.result['summary'] = {
            'total_devices': topology.get('device_count', 0),
            'total_connections': topology.get('connection_count', 0),
            'device_types': device_types,
            'connection_types': connection_types,
            'efz_files_parsed': len(devices_config),
            'topology_version': topology.get('version', 'N/A')
        }
    
    def save_results(self, output_file: str = "ensp_parse_results.json"):
        """保存解析结果到JSON文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.result, f, ensure_ascii=False, indent=2)
            print(f"\n✓ 解析结果已保存到: {output_file}")
        except Exception as e:
            print(f"\n✗ 保存结果失败: {e}")
    
    def print_summary(self):
        """打印解析摘要"""
        summary = self.result.get('summary', {})
        
        print(f"\n=== 解析摘要 ===")
        print(f"拓扑版本: {summary.get('topology_version', 'N/A')}")
        print(f"设备总数: {summary.get('total_devices', 0)}")
        print(f"连接总数: {summary.get('total_connections', 0)}")
        print(f"EFZ文件: {summary.get('efz_files_parsed', 0)}")
        
        print(f"\n设备类型分布:")
        for device_type, count in summary.get('device_types', {}).items():
            print(f"  - {device_type}: {count}")
        
        print(f"\n连接类型分布:")
        for conn_type, count in summary.get('connection_types', {}).items():
            print(f"  - {conn_type}: {count}")

def main():
    """主函数"""
    parser = ENSPParser()
    
    # 执行解析
    results = parser.parse_all()
    
    # 打印摘要
    parser.print_summary()
    
    # 保存结果
    parser.save_results()
    
    print(f"\n解析完成! 详细结果请查看 ensp_parse_results.json 文件")

if __name__ == "__main__":
    main()
