{"parse_time": "2025-06-01T06:19:14.008763", "workspace": ".", "topology": {"version": "**********", "devices": {"B973542B-6760-40cb-A1A1-5B9258728B3B": {"id": "B973542B-6760-40cb-A1A1-5B9258728B3B", "name": "AR1", "model": "AR201", "system_mac": "00-E0-FC-3F-08-F0", "com_port": "2000", "poe": false, "bootmode": "0", "position": {"x": 198.0, "y": 76.0, "edit_left": 225, "edit_top": 130}, "interfaces": [{"number": "slot17", "is_main_board": true, "interfaces": [{"type": "Ethernet", "name": "Ethernet", "count": 8}, {"type": "Ethernet", "name": "Ethernet", "count": 1}]}], "settings": ""}, "CAD83C05-670C-4ef5-A165-6F7BCB8BBA61": {"id": "CAD83C05-670C-4ef5-A165-6F7BCB8BBA61", "name": "LSW1", "model": "S5700", "system_mac": "4C-1F-CC-FE-67-9B", "com_port": "2001", "poe": false, "bootmode": "0", "position": {"x": 205.0, "y": 223.0, "edit_left": 232, "edit_top": 277}, "interfaces": [{"number": "slot17", "is_main_board": true, "interfaces": [{"type": "Ethernet", "name": "GE", "count": 24}]}], "settings": ""}, "C1548A47-90F6-42ac-AE86-F7AB4B9B73C2": {"id": "C1548A47-90F6-42ac-AE86-F7AB4B9B73C2", "name": "PC1", "model": "PC", "system_mac": "54-89-98-8C-16-F1", "com_port": "0", "poe": false, "bootmode": "0", "position": {"x": 119.0, "y": 461.0, "edit_left": 146, "edit_top": 515}, "interfaces": [{"number": "slot17", "is_main_board": true, "interfaces": [{"type": "Ethernet", "name": "Ethernet", "count": 1}]}], "settings": " -simpc_ip 0.0.0.0  -simpc_mask 0.0.0.0  -simpc_gateway 0.0.0.0  -simpc_mac 54-89-98-8C-16-F1  -simpc_mc_dstip 0.0.0.0  -simpc_mc_dstmac 00-00-00-00-00-00  -simpc_dns1 0.0.0.0  -simpc_dns2 0.0.0.0  -simpc_ipv6 ::  -simpc_prefix 128  -simpc_gatewayv6 ::  -simpc_dhcp_state 0  -simpc_dhcpv6_state 0  -simpc_dns_auto_state 0  -simpc_igmp_version 1  -simpc_group_ip_start 0.0.0.0  -simpc_src_ip_start 0.0.0.0  -simpc_group_num 0  -simpc_group_step 0  -simpc_src_num 0  -simpc_src_step 0  -simpc_type MODE_IS_INCLUDE ", "pc_config": {"simpc_ip": "0.0.0.0", "simpc_mask": "0.0.0.0", "simpc_gateway": "0.0.0.0", "simpc_mac": "54-89-98-8C-16-F1", "simpc_mc_dstip": "0.0.0.0", "simpc_mc_dstmac": "00-00-00-00-00-00", "simpc_dns1": "0.0.0.0", "simpc_dns2": "0.0.0.0", "simpc_ipv6": "::", "simpc_prefix": "128", "simpc_gatewayv6": "::", "simpc_dhcp_state": "0", "simpc_dhcpv6_state": "0", "simpc_dns_auto_state": "0", "simpc_igmp_version": "1", "simpc_group_ip_start": "0.0.0.0", "simpc_src_ip_start": "0.0.0.0", "simpc_group_num": "0", "simpc_group_step": "0", "simpc_src_num": "0", "simpc_src_step": "0", "simpc_type": "MODE_IS_INCLUDE"}}, "D4B27BF6-D56E-4a1f-9259-9D668744CD58": {"id": "D4B27BF6-D56E-4a1f-9259-9D668744CD58", "name": "PC2", "model": "PC", "system_mac": "54-89-98-71-50-C0", "com_port": "0", "poe": false, "bootmode": "0", "position": {"x": 314.0, "y": 473.0, "edit_left": 341, "edit_top": 527}, "interfaces": [{"number": "slot17", "is_main_board": true, "interfaces": [{"type": "Ethernet", "name": "Ethernet", "count": 1}]}], "settings": " -simpc_ip 0.0.0.0  -simpc_mask 0.0.0.0  -simpc_gateway 0.0.0.0  -simpc_mac 54-89-98-71-50-C0  -simpc_mc_dstip 0.0.0.0  -simpc_mc_dstmac 00-00-00-00-00-00  -simpc_dns1 0.0.0.0  -simpc_dns2 0.0.0.0  -simpc_ipv6 ::  -simpc_prefix 128  -simpc_gatewayv6 ::  -simpc_dhcp_state 0  -simpc_dhcpv6_state 0  -simpc_dns_auto_state 0  -simpc_igmp_version 1  -simpc_group_ip_start 0.0.0.0  -simpc_src_ip_start 0.0.0.0  -simpc_group_num 0  -simpc_group_step 0  -simpc_src_num 0  -simpc_src_step 0  -simpc_type MODE_IS_INCLUDE ", "pc_config": {"simpc_ip": "0.0.0.0", "simpc_mask": "0.0.0.0", "simpc_gateway": "0.0.0.0", "simpc_mac": "54-89-98-71-50-C0", "simpc_mc_dstip": "0.0.0.0", "simpc_mc_dstmac": "00-00-00-00-00-00", "simpc_dns1": "0.0.0.0", "simpc_dns2": "0.0.0.0", "simpc_ipv6": "::", "simpc_prefix": "128", "simpc_gatewayv6": "::", "simpc_dhcp_state": "0", "simpc_dhcpv6_state": "0", "simpc_dns_auto_state": "0", "simpc_igmp_version": "1", "simpc_group_ip_start": "0.0.0.0", "simpc_src_ip_start": "0.0.0.0", "simpc_group_num": "0", "simpc_group_step": "0", "simpc_src_num": "0", "simpc_src_step": "0", "simpc_type": "MODE_IS_INCLUDE"}}, "B9407E96-851C-4284-B28C-819E7F714866": {"id": "B9407E96-851C-4284-B28C-819E7F714866", "name": "LSW2", "model": "S3700", "system_mac": "4C-1F-CC-E7-06-D8", "com_port": "2002", "poe": false, "bootmode": "0", "position": {"x": 213.0, "y": 358.0, "edit_left": 240, "edit_top": 412}, "interfaces": [{"number": "slot17", "is_main_board": true, "interfaces": [{"type": "Ethernet", "name": "Ethernet", "count": 22}, {"type": "Ethernet", "name": "GE", "count": 2}]}], "settings": ""}}, "connections": [{"src_device_id": "B973542B-6760-40cb-A1A1-5B9258728B3B", "dest_device_id": "CAD83C05-670C-4ef5-A165-6F7BCB8BBA61", "interface_pairs": [{"line_name": "Copper", "src_index": 0, "dest_index": 0, "src_position": {"x": 227.054047, "y": 146.134888}, "dest_position": {"x": 229.945953, "y": 206.865112}}]}, {"src_device_id": "CAD83C05-670C-4ef5-A165-6F7BCB8BBA61", "dest_device_id": "B9407E96-851C-4284-B28C-819E7F714866", "interface_pairs": [{"line_name": "Copper", "src_index": 1, "dest_index": 0, "src_position": {"x": 234.55455, "y": 293.108154}, "dest_position": {"x": 237.44545, "y": 341.891846}}]}, {"src_device_id": "B9407E96-851C-4284-B28C-819E7F714866", "dest_device_id": "C1548A47-90F6-42ac-AE86-F7AB4B9B73C2", "interface_pairs": [{"line_name": "Copper", "src_index": 1, "dest_index": 0, "src_position": {"x": 210.889847, "y": 416.897278}, "dest_position": {"x": 175.110153, "y": 456.102722}}]}, {"src_device_id": "B9407E96-851C-4284-B28C-819E7F714866", "dest_device_id": "D4B27BF6-D56E-4a1f-9259-9D668744CD58", "interface_pairs": [{"line_name": "Copper", "src_index": 2, "dest_index": 0, "src_position": {"x": 268.496582, "y": 417.446594}, "dest_position": {"x": 312.503418, "y": 467.553406}}]}], "device_count": 5, "connection_count": 4}, "devices_config": {"B9407E96-851C-4284-B28C-819E7F714866": {"file_path": ".\\B9407E96-851C-4284-B28C-819E7F714866\\flash.efz", "file_size": 4676, "is_valid_efz": true, "header_info": {"signature": "eNSP", "header_hex": "654e5350000000005000000000100080ffffffffffffff3fffffffffffffff3f"}, "content_analysis": {"readable_text_count": 8, "sample_text": ["rr.dat", "rr.bak", "compatible"]}, "config_files": ["vrpcfg.zip", "src", "rr.dat", "rr.bak", "compatible", "patchfile.src", "patchfilenext.src"]}, "CAD83C05-670C-4ef5-A165-6F7BCB8BBA61": {"file_path": ".\\CAD83C05-670C-4ef5-A165-6F7BCB8BBA61\\flash.efz", "file_size": 4676, "is_valid_efz": true, "header_info": {"signature": "eNSP", "header_hex": "654e5350000000005000000000100080ffffffffffffff3fffffffffffffff3f"}, "content_analysis": {"readable_text_count": 8, "sample_text": ["rr.dat", "rr.bak", "compatible"]}, "config_files": ["vrpcfg.zip", "src", "rr.dat", "rr.bak", "compatible", "patchfile.src", "patchfilenext.src"]}}, "summary": {"total_devices": 5, "total_connections": 4, "device_types": {"AR201": 1, "S5700": 1, "PC": 2, "S3700": 1}, "connection_types": {"Copper": 4}, "efz_files_parsed": 2, "topology_version": "**********"}}