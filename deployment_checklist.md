# 单臂路由配置部署检查清单
# 生成时间: 2025-06-01 06:25:16

## 准备工作
[ ] 启动eNSP软件
[ ] 打开拓扑文件 (1.topo)
[ ] 启动所有设备 (AR1, LSW1, LSW2)
[ ] 确认设备启动完成

## AR1路由器配置
[ ] 进入系统视图 (system-view)
[ ] 配置系统名称 (sysname AR1)
[ ] 配置主接口 Ethernet0/0/0
[ ] 配置子接口 Ethernet0/0/0.10 (VLAN10)
[ ] 配置子接口 Ethernet0/0/0.20 (VLAN20)
[ ] 保存配置 (save)

## LSW1交换机配置
[ ] 进入系统视图 (system-view)
[ ] 配置系统名称 (sysname LSW1)
[ ] 创建VLAN (vlan batch 10 20)
[ ] 配置Trunk接口 GE0/0/1 (连接AR1)
[ ] 配置Trunk接口 GE0/0/2 (连接LSW2)
[ ] 启用STP (stp enable)
[ ] 保存配置 (save)

## LSW2交换机配置
[ ] 进入系统视图 (system-view)
[ ] 配置系统名称 (sysname LSW2)
[ ] 创建VLAN (vlan batch 10 20)
[ ] 配置Trunk接口 GE0/0/1 (连接LSW1)
[ ] 配置Access接口 Eth0/0/1 VLAN10 (连接PC1)
[ ] 配置Access接口 Eth0/0/2 VLAN20 (连接PC2)
[ ] 启用STP (stp enable)
[ ] 保存配置 (save)

## PC配置
[ ] PC1: IP *************, 网关 ************
[ ] PC2: IP *************, 网关 ************

## 验证测试
[ ] PC1 ping 网关 (************)
[ ] PC2 ping 网关 (************)
[ ] PC1 ping PC2 (*************)
[ ] PC2 ping PC1 (*************)
[ ] 检查路由表 (display ip routing-table)
[ ] 检查VLAN状态 (display vlan)
[ ] 检查STP状态 (display stp brief)

## 完成确认
[ ] 所有ping测试成功
[ ] 网络配置正确
[ ] 保存所有设备配置
[ ] 记录配置完成时间

---
配置完成签名: ________________  日期: ________________