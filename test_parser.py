#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版本的eNSP解析器测试
"""

import xml.etree.ElementTree as ET
import os

def parse_topo_simple():
    """简单解析拓扑文件"""
    print("=== eNSP拓扑图文件解析器 ===")
    
    topo_file = "1.topo"
    if not os.path.exists(topo_file):
        print(f"错误: 文件 {topo_file} 不存在")
        return
    
    try:
        # 读取文件并处理编码问题
        with open(topo_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # 替换不支持的编码声明
        content = content.replace('encoding="UNICODE"', 'encoding="UTF-8"')

        # 解析XML
        root = ET.fromstring(content)
        
        print(f"拓扑文件版本: {root.get('version', 'N/A')}")
        
        # 解析设备
        devices_element = root.find('devices')
        devices = []
        if devices_element is not None:
            for dev in devices_element.findall('dev'):
                device = {
                    'id': dev.get('id', ''),
                    'name': dev.get('name', ''),
                    'model': dev.get('model', ''),
                    'mac': dev.get('system_mac', ''),
                    'port': dev.get('com_port', ''),
                    'x': dev.get('cx', '0'),
                    'y': dev.get('cy', '0')
                }
                devices.append(device)
        
        print(f"\n发现 {len(devices)} 个设备:")
        for i, device in enumerate(devices, 1):
            print(f"  {i}. {device['name']} ({device['model']})")
            print(f"     MAC: {device['mac']}, 端口: {device['port']}")
            print(f"     位置: ({device['x']}, {device['y']})")
        
        # 解析连接
        lines_element = root.find('lines')
        connections = []
        if lines_element is not None:
            for line in lines_element.findall('line'):
                connection = {
                    'src': line.get('srcDeviceID', ''),
                    'dest': line.get('destDeviceID', '')
                }
                connections.append(connection)
        
        print(f"\n发现 {len(connections)} 个连接:")
        device_dict = {d['id']: d['name'] for d in devices}
        for i, conn in enumerate(connections, 1):
            src_name = device_dict.get(conn['src'], 'Unknown')
            dest_name = device_dict.get(conn['dest'], 'Unknown')
            print(f"  {i}. {src_name} <-> {dest_name}")
        
    except Exception as e:
        print(f"解析失败: {e}")

def parse_efz_simple():
    """简单解析EFZ文件"""
    print("\n=== EFZ文件解析 ===")
    
    # 查找EFZ文件
    efz_files = []
    for item in os.listdir('.'):
        if os.path.isdir(item):
            efz_path = os.path.join(item, 'flash.efz')
            if os.path.exists(efz_path):
                efz_files.append(efz_path)
    
    print(f"发现 {len(efz_files)} 个EFZ文件:")
    
    for efz_file in efz_files:
        print(f"\n分析文件: {efz_file}")
        try:
            with open(efz_file, 'rb') as f:
                data = f.read()
            
            print(f"  文件大小: {len(data)} 字节")
            
            # 检查文件头
            if data.startswith(b'eNSP'):
                print("  文件类型: eNSP EFZ格式")
            else:
                print("  警告: 不是标准的eNSP EFZ格式")
            
            # 查找关键字
            keywords = [b'vrpcfg.zip', b'src', b'rr.dat', b'rr.bak', b'compatible']
            found_keywords = []
            for keyword in keywords:
                if keyword in data:
                    found_keywords.append(keyword.decode('ascii'))
            
            if found_keywords:
                print(f"  包含的配置文件: {', '.join(found_keywords)}")
            else:
                print("  未发现配置文件引用")
                
        except Exception as e:
            print(f"  解析失败: {e}")

if __name__ == "__main__":
    parse_topo_simple()
    parse_efz_simple()
    print("\n解析完成!")
