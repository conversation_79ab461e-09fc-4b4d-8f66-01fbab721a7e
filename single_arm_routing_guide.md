# 单臂路由配置指南

**基于eNSP拓扑的单臂路由实现方案**

---

## 📋 项目概述

本项目基于解析的eNSP网络拓扑，实现单臂路由配置，使不同VLAN的PC能够通过路由器进行跨网段通信。

### 🌐 网络拓扑结构

```
🔀 AR1 (AR201路由器)
│
│ Trunk (VLAN 10,20)
│ Ethernet0/0/0
│
🔄 LSW1 (S5700交换机)
│
│ Trunk (VLAN 10,20)  
│ GE0/0/2
│
🔄 LSW2 (S3700交换机)
├─ Access VLAN 10 ──── 💻 PC1 (*************)
│  Ethernet0/0/1
│
└─ Access VLAN 20 ──── 💻 PC2 (*************)
   Ethernet0/0/2
```

---

## 🎯 配置目标

1. **VLAN隔离**: PC1和PC2分别属于不同VLAN，实现二层隔离
2. **跨VLAN通信**: 通过路由器子接口实现VLAN间路由
3. **单臂路由**: 路由器只使用一个物理接口连接交换机
4. **网络安全**: 配置STP防止环路，确保网络稳定

---

## 📊 IP地址规划

| 设备 | 接口 | IP地址 | VLAN | 描述 |
|------|------|--------|------|------|
| AR1 | Ethernet0/0/0.10 | ************/24 | 10 | PC1网段网关 |
| AR1 | Ethernet0/0/0.20 | ************/24 | 20 | PC2网段网关 |
| PC1 | 网卡 | *************/24 | 10 | 终端设备 |
| PC2 | 网卡 | *************/24 | 20 | 终端设备 |

### 🔗 VLAN规划

- **VLAN 10**: PC1网段 (************/24)
- **VLAN 20**: PC2网段 (************/24)

---

## ⚙️ 设备配置详解

### 🔀 AR1 路由器配置

**核心功能**: 单臂路由，VLAN间路由

```bash
# 系统配置
sysname AR1

# 主接口配置
interface Ethernet0/0/0
 description 连接到LSW1-GE0/0/0
 undo shutdown

# VLAN 10 子接口 (PC1网段网关)
interface Ethernet0/0/0.10
 description VLAN10-PC1网段
 dot1q termination vid 10
 ip address ************ *************
 arp broadcast enable

# VLAN 20 子接口 (PC2网段网关)
interface Ethernet0/0/0.20
 description VLAN20-PC2网段
 dot1q termination vid 20
 ip address ************ *************
 arp broadcast enable
```

**关键配置说明**:
- `dot1q termination vid`: 配置子接口终结指定VLAN
- `arp broadcast enable`: 启用ARP广播，确保正常通信

### 🔄 LSW1 交换机配置

**核心功能**: VLAN中继，连接路由器和下级交换机

```bash
# 系统配置
sysname LSW1

# 创建VLAN
vlan batch 10 20

# 连接路由器的Trunk接口
interface GigabitEthernet0/0/1
 description 连接到AR1-Ethernet0/0/0
 port link-type trunk
 port trunk allow-pass vlan 10 20
 undo shutdown

# 连接LSW2的Trunk接口
interface GigabitEthernet0/0/2
 description 连接到LSW2-GE0/0/1
 port link-type trunk
 port trunk allow-pass vlan 10 20
 undo shutdown

# STP配置
stp enable
stp mode rstp
```

### 🔄 LSW2 交换机配置

**核心功能**: 接入层交换，PC设备VLAN分配

```bash
# 系统配置
sysname LSW2

# 创建VLAN
vlan batch 10 20

# 连接LSW1的Trunk接口
interface GigabitEthernet0/0/1
 description 连接到LSW1-GE0/0/2
 port link-type trunk
 port trunk allow-pass vlan 10 20
 undo shutdown

# PC1接入接口 (VLAN 10)
interface Ethernet0/0/1
 description 连接到PC1
 port link-type access
 port default vlan 10
 undo shutdown

# PC2接入接口 (VLAN 20)
interface Ethernet0/0/2
 description 连接到PC2
 port link-type access
 port default vlan 20
 undo shutdown
```

---

## 💻 PC配置

### PC1 配置 (VLAN 10)

**Windows系统**:
```cmd
netsh interface ip set address "本地连接" static ************* ************* ************
netsh interface ip set dns "本地连接" static *******
```

**Linux系统**:
```bash
ifconfig eth0 ************* netmask *************
route add default gw ************
echo 'nameserver *******' > /etc/resolv.conf
```

### PC2 配置 (VLAN 20)

**Windows系统**:
```cmd
netsh interface ip set address "本地连接" static ************* ************* ************
netsh interface ip set dns "本地连接" static *******
```

**Linux系统**:
```bash
ifconfig eth0 ************* netmask *************
route add default gw ************
echo 'nameserver *******' > /etc/resolv.conf
```

---

## 🧪 配置验证

### 1. 基本连通性测试

```bash
# PC1 ping 网关
ping ************

# PC2 ping 网关  
ping ************

# PC1 ping PC2 (跨VLAN通信)
ping *************

# PC2 ping PC1 (跨VLAN通信)
ping *************
```

### 2. 路由表检查

**AR1路由器**:
```bash
display ip routing-table
display interface brief
```

**交换机VLAN检查**:
```bash
display vlan
display port vlan
display stp brief
```

### 3. 预期结果

✅ **成功指标**:
- PC1和PC2都能ping通各自网关
- PC1和PC2能够相互ping通
- 路由表显示正确的直连路由
- VLAN配置正确，STP无环路

---

## 🔧 故障排除

### 常见问题及解决方案

1. **PC无法ping通网关**
   - 检查PC IP配置是否正确
   - 检查交换机接口VLAN配置
   - 检查接口是否启用 (`undo shutdown`)

2. **跨VLAN无法通信**
   - 检查路由器子接口配置
   - 检查Trunk接口VLAN允许列表
   - 检查路由表是否有对应路由

3. **网络环路问题**
   - 检查STP配置和状态
   - 确认没有物理环路连接

---

## 📁 配置文件清单

生成的配置文件位于 `single_arm_routing_configs/` 目录：

- `ar1_config.txt` - AR1路由器配置
- `lsw1_config.txt` - LSW1交换机配置  
- `lsw2_config.txt` - LSW2交换机配置
- `complete_config.json` - 完整配置数据(JSON格式)

---

## 🎯 配置要点总结

1. **单臂路由核心**: 路由器子接口 + VLAN标签
2. **Trunk配置**: 交换机间传输多VLAN流量
3. **Access配置**: PC接入指定VLAN
4. **STP防环**: 确保网络稳定性
5. **IP规划**: 合理的地址分配和网关设置

---

*配置生成时间: 2025-06-01*  
*基于eNSP拓扑解析结果自动生成*
